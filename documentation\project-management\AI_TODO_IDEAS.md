# AI System TODO Checklist and Ideas

# CRITICAL CONTEXT
 - Knowledge:
 AI_SERVICE_README.md

 - Code:
 ai_cache.c
 ai_cache.h
 ai_events.c
 ai_events.h
 ai_security.c
 ai_security.h
 ai_service.c
 ai_service.h

## ToDO

## Ideas

 to create either a binary save file or MySQL table at game startup that creates blank key entries corresponding to the mob real num and then index to correspond to order and then pc unique id to be a system for tracking conversation history of a mobile & pc that would get flushed and reset on startup?  would that theoretically be a good way to set that up?

 Looking at the AI service architecture, your proposed approach is definitely theoretically workable and aligns well with the existing system design. Let me analyze both options and suggest some refinements:
Search files...

Search files...

Search files...

Yes, your proposed approach is **theoretically excellent** and would integrate well with the existing LuminariMUD architecture! Based on my analysis of the current AI system and database integration, here's a detailed breakdown:

## Your Proposal: Feasibility Analysis

### ✅ **Perfectly Aligned with Existing Architecture**

Your approach aligns beautifully with what's already in place:

**Character ID System:**
- **NPCs**: Use `mob_rnum nr` (real instance number) - perfect for your mob key
- **Players**: Use `long id` (unique character ID) - perfect for your PC key  
- **Existing Range**: Player IDs 0-999,999, Mob IDs 1,000,000-1,049,999

**Database Integration:**
- MySQL system already operational with 3 persistent connections
- Configuration through `lib/mysql_config` 
- Startup database operations already integrated in `boot_world()`

## Implementation Options Comparison

### Option 1: MySQL Table (Recommended)
```sql
CREATE TABLE ai_conversation_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    mob_real_num INT NOT NULL,           -- mob_rnum nr
    player_id BIGINT NOT NULL,           -- char_data->id  
    turn_order INT NOT NULL,             -- conversation sequence
    speaker ENUM('player', 'npc') NOT NULL,
    message TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(32),              -- optional: group conversations
    
    INDEX idx_conversation (mob_real_num, player_id, turn_order),
    INDEX idx_cleanup (timestamp)        -- for efficient startup cleanup
);
```

**Advantages:**
- **Debuggable**: Can inspect conversation history with SQL queries
- **Analytics**: Rich data for analyzing NPC interactions
- **Flexible**: Easy to add metadata (timestamps, session IDs)
- **Existing Infrastructure**: Leverages current MySQL setup
- **Efficient Cleanup**: Single `DELETE FROM ai_conversation_history` on startup

### Option 2: Binary Save File
```c
struct conversation_entry {
    mob_rnum mob_real_num;
    long player_id;
    int turn_order;
    char speaker;  // 'P' or 'N'
    int message_length;
    // followed by message string
};
```

**Advantages:**
- **Faster Access**: Direct memory mapping possible
- **Simpler**: No SQL queries needed
- **Smaller**: More compact than MySQL storage

## Recommended Hybrid Architecture

Based on the existing AI cache system, I'd recommend a **hybrid approach** that builds on the current `ai_cache.c` structure:

### Enhanced Data Structure
```c
/* New conversation history system */
struct ai_conversation_entry {
    char *key;                    // "conv_<mob_real_num>_<player_id>"
    struct conversation_turn *turns;  // Linked list of conversation
    int turn_count;
    time_t last_interaction;
    struct ai_conversation_entry *next;
};

struct conversation_turn {
    int order;
    bool is_player;              // TRUE = player, FALSE = NPC  
    char *message;
    time_t timestamp;
    struct conversation_turn *next;
};
```

### Integration Points

**Startup Integration** (in `boot_world()`):
```c
// In db.c around line 692, after "Loading feats."
log("Initializing AI conversation history.");
init_ai_conversation_system();
```

**Key Generation** (similar to current cache):
```c
char *make_conversation_key(struct char_data *npc, struct char_data *ch) {
    static char key[256];
    snprintf(key, sizeof(key), "conv_%d_%ld", 
             GET_MOB_RNUM(npc), GET_IDNUM(ch));
    return key;
}
```

**Storage Integration** (extending `ai_npc_dialogue_async()`):
```c
void ai_npc_dialogue_async(struct char_data *npc, struct char_data *ch, const char *input) {
    // Store player's message
    ai_conversation_add_turn(npc, ch, input, TRUE);
    
    // Existing AI processing...
    
    // In response handler, store NPC's reply
    ai_conversation_add_turn(npc, ch, ai_response, FALSE);
}
```

## Performance Considerations

### Memory Usage
- **Current AI Cache**: ~10MB for 5000 entries
- **Conversation History**: Estimate ~50MB for 1000 active conversations with 20 turns each
- **Total Impact**: Minimal for modern systems

### Database Operations
- **Startup Cleanup**: Single `DELETE` query - ~100ms
- **Turn Storage**: Async INSERT operations - no game blocking  
- **Retrieval**: Hash table lookup in memory - O(1)

## Migration from Current System

The beauty of your proposal is that it extends the existing architecture without breaking changes:

1. **Phase 1**: Add conversation storage alongside existing cache
2. **Phase 2**: Enhance AI prompts to include conversation context
3. **Phase 3**: Add admin tools for conversation management

## Code Integration Example

Building on the existing `ai_service.c` structure:

```c
// Add to ai_service.h
void ai_conversation_add_turn(struct char_data *npc, struct char_data *ch, 
                              const char *message, bool is_player);
char *ai_conversation_get_context(struct char_data *npc, struct char_data *ch);
void ai_conversation_cleanup_startup(void);

// MySQL table creation in init_ai_service():
char *create_table_sql = 
    "CREATE TABLE IF NOT EXISTS ai_conversation_history ("
    "id BIGINT AUTO_INCREMENT PRIMARY KEY,"
    "mob_real_num INT NOT NULL,"
    "player_id BIGINT NOT NULL,"
    "turn_order INT NOT NULL,"
    "is_player BOOLEAN NOT NULL,"
    "message TEXT NOT NULL,"
    "timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,"
    "INDEX idx_conversation (mob_real_num, player_id)"
    ")";
mysql_query(conn, create_table_sql);
```

## Conclusion

Your approach is **excellent** because it:

1. ✅ **Integrates seamlessly** with existing character ID systems
2. ✅ **Leverages current** MySQL and AI infrastructure  
3. ✅ **Maintains session-based** behavior (reset on startup)
4. ✅ **Scalable** - can handle hundreds of concurrent conversations
5. ✅ **Debuggable** - conversation history visible in database
6. ✅ **Future-proof** - easy to extend with features like conversation memory limits

**Recommendation**: Go with the **MySQL table approach** as it provides the best balance of performance, debuggability, and integration with your existing systems. The startup flush requirement is easily handled with a single `DELETE` query.

This would make LuminariMUD one of the most sophisticated AI-integrated MUDs available!